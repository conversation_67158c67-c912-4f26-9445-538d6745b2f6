'use strict';

const { ListEndpointBaseTests } = require('../../listEndpointBaseTests');
const Agent = require('../../../data/backend/SMSDB/agent');
const Contact = require('../../../data/backend/SMSDB/contact');
const CompanyContact = require('../../../data/backend/SMSDB/companyContact');
const LeadStatusView = require('../../../data/datahub/merchant/leadStatusView');
const AgentInfo = require('../../../data/datahub/merchant/agentInfo');

class PartnerLeadsListEndpointTest extends ListEndpointBaseTests {
  #agent;
  #contact;
  #companyContact;
  #leadStatusView;
  #agentInfo;

  constructor() {
    super('../../routes/v1/partner-leads/list');

    this.#agent = new Agent(this.testContext.backEndDatabase.SMSDB);
    this.#contact = new Contact(this.testContext.backEndDatabase.SMSDB);
    this.#companyContact = new CompanyContact(this.testContext.backEndDatabase.SMSDB);
    this.#leadStatusView = new LeadStatusView(this.testContext.datahubDatabase);
    this.#agentInfo = new AgentInfo(this.testContext.datahubDatabase);
  }

  async seedAgentRecordAsync(recordNumber, agentLocationId = null) {
    const agentId = recordNumber;
    const contactId = recordNumber;

    const agentData = {
      AgentId: agentId,
      ParentId: agentId,
      ZeamsterLocID: agentLocationId || this.testContext.userLocationIds[0],
      Status: 1,
    };

    await this.#agent.createRecordAsync(
      {
        CompanyId: agentId,
      },
      agentData,
    );

    await this.#contact.createRecordAsync({
      ContactId: contactId,
    });

    await this.#companyContact.createRecordAsync({
      CompanyId: agentId,
      ContactId: contactId,
      ContactType: 1,
    });
  }

  async seedAgentInfoRecordAsync(agentId, agentLocationId, officeLocationId = null) {
    await this.#agentInfo.createAgentInfoWithLocations(agentId, agentLocationId, officeLocationId);
  }

  async seedLeadStatusRecordAsync(recordNumber, agentId, agentLocationId, officeLocationId = null) {
    await this.#leadStatusView.createLeadWithSpecificAgent(recordNumber, agentId, agentLocationId, officeLocationId);
  }

  async seedDataForCheckingResponsesMatchAsync() {
    const agentId = 679;
    const agentLocationId = this.testContext.userLocationIds[0];
    
    // Seed agent record
    await this.seedAgentRecordAsync(agentId, agentLocationId);
    
    // Seed agent info record
    await this.seedAgentInfoRecordAsync(agentId, agentLocationId);
    
    // Seed lead status record
    await this.seedLeadStatusRecordAsync(1, agentId, agentLocationId);
  }

  async seedDataForFilterByAsync() {
    const agentId1 = 918;
    const agentId2 = 657;
    const agentLocationId = this.testContext.userLocationIds[0];

    // Seed first agent and leads
    await this.seedAgentRecordAsync(agentId1, agentLocationId);
    await this.seedAgentInfoRecordAsync(agentId1, agentLocationId);
    await this.seedLeadStatusRecordAsync(2, agentId1, agentLocationId);
    await this.seedLeadStatusRecordAsync(3, agentId1, agentLocationId);

    // Seed second agent and leads
    await this.seedAgentRecordAsync(agentId2, agentLocationId);
    await this.seedAgentInfoRecordAsync(agentId2, agentLocationId);
    await this.seedLeadStatusRecordAsync(4, agentId2, agentLocationId);
  }

  testsToSkip() {
    return [this.TestNames.PagingResultsDataForOrderingChildRecords];
  }

  filterAndOrderFieldsToSkip = [
    // Skip fields that might be complex to filter/order or contain sensitive data
    'lost_opportunity_explanation',
    'other_closed_won_reason',
  ];

  filterByFieldsToSkip() {
    return this.filterAndOrderFieldsToSkip;
  }

  orderByFieldsToSkip() {
    return this.filterAndOrderFieldsToSkip;
  }

  runTests() {
    super.runTests();

    it('should return 403 for user without access to endpoint', async () => {
      // This test would require setting up a user without proper access
      // For now, we'll skip this as it requires more complex auth setup
    });

    it('should filter leads by agent location access', async () => {
      // Arrange
      const agentId = 891;
      const restrictedLocationId = this.testContext.notUserLocations[0];

      await this.seedAgentRecordAsync(agentId, restrictedLocationId);
      await this.seedAgentInfoRecordAsync(agentId, restrictedLocationId);
      await this.seedLeadStatusRecordAsync(5, agentId, restrictedLocationId);

      // Act
      const responseData = await this.sendRequestAsync();

      // Assert
      expect(responseData.statusCode).toEqual(200);
      const parsedResponse = JSON.parse(responseData.body);

      // Should not include leads from restricted location
      const leadsFromRestrictedLocation = parsedResponse.list.filter(
        lead => lead.agent_location_id === restrictedLocationId
      );
      expect(leadsFromRestrictedLocation).toHaveLength(0);
    });

    it('should return leads with correct data transformation', async () => {
      // Arrange
      const agentId = 123;
      const agentLocationId = this.testContext.userLocationIds[0];

      await this.seedAgentRecordAsync(agentId, agentLocationId);
      await this.seedAgentInfoRecordAsync(agentId, agentLocationId);
      await this.seedLeadStatusRecordAsync(6, agentId, agentLocationId);

      // Act
      const responseData = await this.sendRequestAsync();

      // Assert
      expect(responseData.statusCode).toEqual(200);
      const parsedResponse = JSON.parse(responseData.body);

      expect(parsedResponse).toHaveProperty('list');
      expect(parsedResponse).toHaveProperty('type', 'leads');
      expect(parsedResponse).toHaveProperty('pagination');

      if (parsedResponse.list.length > 0) {
        const lead = parsedResponse.list[0];
        expect(lead).toHaveProperty('hubspot_company_id');
        expect(lead).toHaveProperty('fortis_id');
        expect(lead).toHaveProperty('company_name');
        expect(lead).toHaveProperty('first_name');
        expect(lead).toHaveProperty('last_name');
        expect(lead).toHaveProperty('email');
        expect(lead).toHaveProperty('phone');
        expect(lead).toHaveProperty('agent_id');
        expect(lead).toHaveProperty('agent_location_id');
        expect(lead).toHaveProperty('office_location_id');
      }
    });

    it('should handle pagination correctly', async () => {
      // Arrange
      const agentId = 456;
      const agentLocationId = this.testContext.userLocationIds[0];

      await this.seedAgentRecordAsync(agentId, agentLocationId);
      await this.seedAgentInfoRecordAsync(agentId, agentLocationId);

      // Seed multiple leads for pagination testing
      for (let i = 7; i <= 12; i++) {
        await this.seedLeadStatusRecordAsync(i, agentId, agentLocationId);
      }

      // Act - Request with page size limit
      const responseData = await this.sendRequestAsync((request) => {
        request.query = { page_size: 3, page_number: 1 };
      });

      // Assert
      expect(responseData.statusCode).toEqual(200);
      const parsedResponse = JSON.parse(responseData.body);

      expect(parsedResponse.pagination).toHaveProperty('page_size', 3);
      expect(parsedResponse.pagination).toHaveProperty('page_number', 0); // API returns 0-based
      expect(parsedResponse.pagination).toHaveProperty('total_count');
      expect(parsedResponse.pagination.total_count).toBeGreaterThanOrEqual(3);
    });

    it('should handle timestamp conversion correctly', async () => {
      // Arrange
      const agentId = 789;
      const agentLocationId = this.testContext.userLocationIds[0];
      const testDate = new Date('2024-01-15T10:30:00Z');

      await this.seedAgentRecordAsync(agentId, agentLocationId);
      await this.seedAgentInfoRecordAsync(agentId, agentLocationId);

      // Create a lead with specific timestamps using the data setup class
      await this.#leadStatusView.createLeadWithTimestamps(999, {
        hubspot_id: 'hs_timestamp_test',
        fortis_id: 'FORT-TIMESTAMP',
        company_name: 'Timestamp Test Company',
        first_name: 'Time',
        last_name: 'Stamp',
        email: '<EMAIL>',
        phone: '************',
        agent_id: agentId.toString(),
        lead_assigned_date: testDate,
        lead_opened_date: testDate,
        status_entry_date: testDate,
      });

      // Act
      const responseData = await this.sendRequestAsync();

      // Assert
      expect(responseData.statusCode).toEqual(200);
      const parsedResponse = JSON.parse(responseData.body);

      const timestampLead = parsedResponse.list.find(
        lead => lead.hubspot_company_id === 'hs_timestamp_test'
      );

      if (timestampLead) {
        expect(timestampLead.assigned_ts).toBeDefined();
        expect(timestampLead.opened_ts).toBeDefined();
        expect(timestampLead.status_entry_ts).toBeDefined();

        // Timestamps should be Unix timestamps (numbers)
        expect(typeof timestampLead.assigned_ts).toBe('number');
        expect(typeof timestampLead.opened_ts).toBe('number');
        expect(typeof timestampLead.status_entry_ts).toBe('number');
      }
    });

    it('should handle office location filtering correctly', async () => {
      // Arrange
      const agentId = 999;
      const agentLocationId = this.testContext.userLocationIds[0];
      const officeLocationId = this.testContext.userLocationIds[1];

      await this.seedAgentRecordAsync(agentId, agentLocationId);
      await this.seedAgentInfoRecordAsync(agentId, agentLocationId, officeLocationId);
      await this.seedLeadStatusRecordAsync(13, agentId, agentLocationId, officeLocationId);

      // Act
      const responseData = await this.sendRequestAsync();

      // Assert
      expect(responseData.statusCode).toEqual(200);
      const parsedResponse = JSON.parse(responseData.body);

      // Should include leads where user has access to either agent_location_id or office_location_id
      const accessibleLeads = parsedResponse.list.filter(
        lead => this.testContext.userLocationIds.includes(lead.agent_location_id) ||
                this.testContext.userLocationIds.includes(lead.office_location_id)
      );
      expect(accessibleLeads.length).toBeGreaterThan(0);
    });

    it('should return empty list when no leads match user access', async () => {
      // Arrange - Create leads with locations user doesn't have access to
      const agentId = 888;
      const restrictedLocationId = this.testContext.notUserLocations[0];

      await this.seedAgentRecordAsync(agentId, restrictedLocationId);
      await this.seedAgentInfoRecordAsync(agentId, restrictedLocationId);
      await this.seedLeadStatusRecordAsync(14, agentId, restrictedLocationId);

      // Act
      const responseData = await this.sendRequestAsync();

      // Assert
      expect(responseData.statusCode).toEqual(200);
      const parsedResponse = JSON.parse(responseData.body);

      expect(parsedResponse.list).toBeInstanceOf(Array);
      expect(parsedResponse.pagination.total_count).toBeGreaterThanOrEqual(0);

      // Should not include any leads from restricted locations
      const restrictedLeads = parsedResponse.list.filter(
        lead => lead.agent_location_id === restrictedLocationId ||
                lead.office_location_id === restrictedLocationId
      );
      expect(restrictedLeads).toHaveLength(0);
    });

    it('should handle leads with null/empty values correctly', async () => {
      // Arrange
      const agentId = 777;
      const agentLocationId = this.testContext.userLocationIds[0];

      await this.seedAgentRecordAsync(agentId, agentLocationId);
      await this.seedAgentInfoRecordAsync(agentId, agentLocationId);

      // Create a lead with many null values
      await this.#leadStatusView._createRecordAsync(15, {
        agent_id: agentId.toString(),
        hubspot_id: null,
        company_name: null,
        first_name: null,
        last_name: null,
        email: null,
        phone: null,
        lead_qualifying_date: null,
        lead_qualified_date: null,
        lead_discovery_date: null,
        closed_date: null,
        lost_opportunity_date: null,
        lost_opportunity_reason: null,
      });

      // Act
      const responseData = await this.sendRequestAsync();

      // Assert
      expect(responseData.statusCode).toEqual(200);
      const parsedResponse = JSON.parse(responseData.body);

      // Should handle null values gracefully
      const nullValueLead = parsedResponse.list.find(
        lead => lead.agent_id === agentId.toString()
      );

      if (nullValueLead) {
        expect(nullValueLead.hubspot_company_id).toBeNull();
        expect(nullValueLead.qualifying_ts).toBeNull();
        expect(nullValueLead.qualified_ts).toBeNull();
        expect(nullValueLead.discovery_ts).toBeNull();
        expect(nullValueLead.closed_ts).toBeNull();
        expect(nullValueLead.lost_opportunity_ts).toBeNull();
      }
    });

    it('should handle sorting and filtering parameters', async () => {
      // Arrange
      const agentId = 666;
      const agentLocationId = this.testContext.userLocationIds[0];

      await this.seedAgentRecordAsync(agentId, agentLocationId);
      await this.seedAgentInfoRecordAsync(agentId, agentLocationId);

      // Create multiple leads with different statuses
      await this.#leadStatusView.createLeadWithStatus(16, 'Open', 'Prospecting');
      await this.#leadStatusView.createLeadWithStatus(17, 'Closed', 'Closed Won');
      await this.#leadStatusView.createLeadWithStatus(18, 'Lost', 'Closed Lost');

      // Act - Test with sorting
      const responseData = await this.sendRequestAsync((request) => {
        request.query = {
          sort: 'status',
          order: 'asc'
        };
      });

      // Assert
      expect(responseData.statusCode).toEqual(200);
      const parsedResponse = JSON.parse(responseData.body);

      expect(parsedResponse.list).toBeInstanceOf(Array);
      expect(parsedResponse.pagination).toBeDefined();
    });
  }
  }
}

new PartnerLeadsListEndpointTest().runTests();
