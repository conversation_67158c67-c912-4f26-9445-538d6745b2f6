const seedDataBase = require('../../seedDataBase');

module.exports = class AgentInfo extends seedDataBase {
  #datahubDatabase;

  constructor(datahubDatabase) {
    super();
    this.#datahubDatabase = datahubDatabase;
  }

  async _createRecordAsync(seedToUse, data) {
    const defaultData = {
      agent_id: seedToUse.toString(),
      agent_location_id: this.generateRandomString(32),
      office_location_id: this.generateRandomString(32),
      created_ts: Math.floor(Date.now() / 1000),
      modified_ts: Math.floor(Date.now() / 1000),
    };

    await this.#datahubDatabase
      .withSchema('merchant')
      .table('agnt_inf')
      .insert({
        ...defaultData,
        ...data
      });
  }

  async createAgentInfoWithLocations(agentId, agentLocationId, officeLocationId = null) {
    return this._createRecordAsync(agentId, {
      agent_id: agentId.toString(),
      agent_location_id: agentLocationId,
      office_location_id: officeLocationId || agentLocationId,
    });
  }

  async createMultipleAgentInfos(agentIds, locationId) {
    const promises = agentIds.map(agentId => 
      this.createAgentInfoWithLocations(agentId, locationId)
    );
    return Promise.all(promises);
  }
};
