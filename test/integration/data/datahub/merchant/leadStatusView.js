const seedDataBase = require('../../seedDataBase');

module.exports = class LeadStatusView extends seedDataBase {
  #datahubDatabase;

  constructor(datahubDatabase) {
    super();
    this.#datahubDatabase = datahubDatabase;
  }

  async _createRecordAsync(seedToUse, data) {
    const defaultData = {
      hubspot_id: `hs_${seedToUse}`,
      fortis_id: `FORT-${seedToUse}`,
      company_name: `Test Company ${seedToUse}`,
      first_name: `<PERSON>${seedToUse}`,
      last_name: `Doe${seedToUse}`,
      email: `test${seedToUse}@example.com`,
      phone: `555-000-${seedToUse.toString().padStart(4, '0')}`,
      pipeline: 'Sales Pipeline',
      deal_name: `Deal ${seedToUse}`,
      deal_stage: this.getRandomFromArray(['Prospecting', 'Qualifying', 'Qualified', 'Discovery', 'Presentation', 'Application', 'Closed Won', 'Closed Lost']),
      status: this.getRandomFromArray(['Open', 'In Progress', 'Closed', 'Lost']),
      office_id: `OFF-${seedToUse}`,
      agent_id: seedToUse.toString(),
      is_inside_agent: this.getRandomFromBool(),
      inside_agent: this.getRandomFromBool() ? `Inside Agent ${seedToUse}` : null,
      referral_source: this.getRandomFromArray(['Website', 'Referral', 'Cold Call', 'Email Campaign', 'Social Media']),
      volume: this.getRandomFromArray(['10000', '25000', '50000', '100000', '250000']),
      platform: this.getRandomFromArray(['HubSpot', 'Salesforce', 'Pipedrive', 'Zoho']),
      lead_assigned_date: this.generateRandomDate(),
      lead_prospecting_date: this.generateRandomDate(),
      lead_qualifying_date: this.getRandomFromBool() ? this.generateRandomDate() : null,
      lead_qualified_date: this.getRandomFromBool() ? this.generateRandomDate() : null,
      lead_discovery_date: this.getRandomFromBool() ? this.generateRandomDate() : null,
      lead_demo_presentation_date: this.getRandomFromBool() ? this.generateRandomDate() : null,
      lead_application_date: this.getRandomFromBool() ? this.generateRandomDate() : null,
      lead_application_submitted_date: this.getRandomFromBool() ? this.generateRandomDate() : null,
      lead_lost_opportunity_date: this.getRandomFromBool() ? this.generateRandomDate() : null,
      lead_lost_opportunity_reason: this.getRandomFromBool() ? this.getRandomFromArray(['Price', 'Competition', 'No Response', 'Not Qualified']) : null,
      lost_opportunity_explanation: this.getRandomFromBool() ? `Lost opportunity explanation ${seedToUse}` : null,
      closed_date: this.getRandomFromBool() ? this.generateRandomDate() : null,
      closed_won_reason: this.getRandomFromBool() ? this.getRandomFromArray(['Best Price', 'Best Service', 'Referral', 'Existing Relationship']) : null,
      other_closed_won_reason: this.getRandomFromBool() ? `Other reason ${seedToUse}` : null,
      status_entry_date: this.generateRandomDate(),
      deal_owner: `Agent ${seedToUse}`,
      lead_opened_date: this.generateRandomDate(),
      client_app_id: `APP-${seedToUse}`,
      website: `https://company${seedToUse}.com`,
    };

    await this.#datahubDatabase
      .withSchema('merchant')
      .table('lead_status_vw')
      .insert({
        ...defaultData,
        ...data
      });
  }

  async createLeadWithSpecificAgent(seedToUse, agentId, agentLocationId, officeLocationId = null) {
    return this._createRecordAsync(seedToUse, {
      agent_id: agentId.toString(),
      // Note: agent_location_id and office_location_id are typically set via the agnt_inf table join
      // but we can override them here if needed for testing
    });
  }

  async createLeadWithTimestamps(seedToUse, timestamps = {}) {
    const timestampData = {};
    
    // Convert any provided timestamps to proper format
    Object.keys(timestamps).forEach(key => {
      if (timestamps[key] instanceof Date) {
        timestampData[key] = timestamps[key];
      }
    });

    return this._createRecordAsync(seedToUse, timestampData);
  }

  async createLeadWithStatus(seedToUse, status, dealStage = null) {
    return this._createRecordAsync(seedToUse, {
      status: status,
      deal_stage: dealStage || this.getRandomFromArray(['Prospecting', 'Qualifying', 'Qualified'])
    });
  }

  async createMultipleLeads(startSeed, count, agentId, commonData = {}) {
    const promises = [];
    for (let i = 0; i < count; i++) {
      promises.push(this._createRecordAsync(startSeed + i, {
        agent_id: agentId.toString(),
        ...commonData
      }));
    }
    return Promise.all(promises);
  }
};
